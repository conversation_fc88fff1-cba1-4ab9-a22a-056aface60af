<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);
require __DIR__ . '/vendor/autoload.php';

$client = new \Google_Client();
$client->setApplicationName('Google Sheets API with PHP');
$client->setScopes([\Google_Service_Sheets::SPREADSHEETS]);
$client->setAuthConfig('odzleads-3fd4a44b7310.json');
$client->setAccessType('offline');

$service = new Google_Service_Sheets($client);

// Spreadsheet details
$spreadsheetId = '';
$sheetName = 'Sheet18';
$range = $sheetName . '!A2:M'; // Full row: A to M



$response = $service->spreadsheets_values->get($spreadsheetId, $range);
$rows = $response->getValues();
/$today = new DateTime();
$cutoffDate = (clone $today)->modify('-7 days');

foreach ($rows as $index => $row) {
    
    $rowNumber = $index + 2;

    $statusCall = strtolower(trim($row[5] ?? ''));  // Column F
    $statusChat = strtolower(trim($row[10] ?? '')); // Column K
    $dateRaw    = trim($row[11] ?? '');             // Column M

    if (empty($dateRaw)) continue;

    // Clean hidden characters
    $dateRaw = preg_replace('/[\x00-\x1F\x7F\xA0]/u', '', $dateRaw);

    // Try multiple date formats
    $formats = ['m/d/Y', 'd/m/Y', 'Y-m-d', 'd-m-Y', 'd.m.Y'];
    $date = null;

    foreach ($formats as $format) {
        $parsed = DateTime::createFromFormat($format, $dateRaw);
        if ($parsed && $parsed->format($format) === $dateRaw) {
            $date = $parsed;
            break;
        }
    }

    // Fallback using strtotime
    if (!$date && strtotime($dateRaw)) {
        $date = new DateTime($dateRaw);
    }

    // Optional: Excel serial date support
    if (!$date && is_numeric($dateRaw) && $dateRaw > 30000) {
        $date = (new DateTime('1899-12-30'))->modify("+$dateRaw days");
    }

    // Still not parsed
    if (!$date) {
        echo "❌ Could not parse date in row $rowNumber: [$dateRaw]\n";
        continue;
    }

    echo "🔍 Row $rowNumber: Parsed Date = " . $date->format('Y-m-d') . "\n";

    if ($date <= $cutoffDate) {
        // Update F (Lead Status Call) if not Awaiting/Hired
        if (!in_array($statusCall, ['awaiting', 'hired'])) {
            $targetRangeF = "$sheetName!F$rowNumber";
            $valueRangeF = new Google_Service_Sheets_ValueRange([
                'range' => $targetRangeF,
                'values' => [['Not Converted']]
            ]);
            $service->spreadsheets_values->update($spreadsheetId, $targetRangeF, $valueRangeF, ['valueInputOption' => 'RAW']);
            echo "✅ Updated F$rowNumber: '$statusCall' -> 'Not Converted'\n";
        }

        // Update K (Lead Status Chat) if not Awaiting/Hired
        if (!in_array($statusChat, ['awaiting', 'hired'])) {
            $targetRangeK = "$sheetName!K$rowNumber";
            $valueRangeK = new Google_Service_Sheets_ValueRange([
                'range' => $targetRangeK,
                'values' => [['Not Converted']]
            ]);
            $service->spreadsheets_values->update($spreadsheetId, $targetRangeK, $valueRangeK, ['valueInputOption' => 'RAW']);
            echo "✅ Updated K$rowNumber: '$statusChat' -> 'Not Converted'\n";
        }
    }
}
?>
